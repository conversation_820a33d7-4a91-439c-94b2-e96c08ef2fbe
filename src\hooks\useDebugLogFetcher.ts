import { useState, useEffect } from 'react';
import axios from 'axios';

export interface DebugLog {
  request_id: string;
  input: string;
  parsed_intent: string;
  tool_called: string;
  tool_success: boolean;
  cache_hit: boolean;
  fallback_used: boolean;
  latency: number;
  vector_fallback_score: number | null;
  timestamp?: string;
  raw_response?: any;
}

interface UseDebugLogFetcherProps {
  requestId: string;
  pollingInterval?: number;
  enabled?: boolean;
}

interface UseDebugLogFetcherReturn {
  debugLog: DebugLog | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

// Mock data scenarios for development
const mockScenarios = {
  'demo-123': {
    input: "Show me dining tables under 50k",
    parsed_intent: "product_lookup",
    tool_called: "get_products_by_price_range",
    tool_success: true,
    cache_hit: false,
    fallback_used: false,
    latency: 2.1,
    vector_fallback_score: null,
  },
  'xyz-123': {
    input: "What's my order status for #ORD12345?",
    parsed_intent: "order_inquiry",
    tool_called: "get_order_status",
    tool_success: true,
    cache_hit: true,
    fallback_used: false,
    latency: 0.8,
    vector_fallback_score: null,
  },
  'error-456': {
    input: "Show me purple unicorns",
    parsed_intent: "product_lookup",
    tool_called: "get_products_by_category",
    tool_success: false,
    cache_hit: false,
    fallback_used: true,
    latency: 4.2,
    vector_fallback_score: 0.234,
  }
};

const getMockDebugLog = (requestId: string): DebugLog => {
  const scenario = mockScenarios[requestId as keyof typeof mockScenarios] || mockScenarios['demo-123'];

  return {
    request_id: requestId,
    ...scenario,
    timestamp: new Date().toISOString(),
    raw_response: {
      products_found: scenario.tool_success ? 4 : 0,
      search_query: scenario.input,
      filters_applied: scenario.tool_success ? ["category:furniture"] : [],
      execution_time: `${scenario.latency}s`,
      cache_status: scenario.cache_hit ? "hit" : "miss",
      fallback_triggered: scenario.fallback_used,
      error_details: !scenario.tool_success ? "No products found matching criteria" : null
    }
  };
};

export const useDebugLogFetcher = ({
  requestId,
  pollingInterval = 1500,
  enabled = true
}: UseDebugLogFetcherProps): UseDebugLogFetcherReturn => {
  const [debugLog, setDebugLog] = useState<DebugLog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDebugLog = async () => {
    try {
      setError(null);

      // For now, use mock data. Replace with actual API call when backend is ready
      // const response = await axios.get(`/api/debug/${requestId}`);
      // setDebugLog(response.data);

      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 200)); // Simulate network delay
      const mockData = getMockDebugLog(requestId);
      setDebugLog(mockData);

    } catch (err) {
      console.error('Error fetching debug log:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch debug log');
    } finally {
      setIsLoading(false);
    }
  };

  const refetch = () => {
    setIsLoading(true);
    fetchDebugLog();
  };

  useEffect(() => {
    if (!enabled || !requestId) return;

    // Initial fetch
    fetchDebugLog();

    // Set up polling
    const interval = setInterval(fetchDebugLog, pollingInterval);

    return () => clearInterval(interval);
  }, [requestId, pollingInterval, enabled]);

  return {
    debugLog,
    isLoading,
    error,
    refetch
  };
};

// Utility function for when the actual API is ready
export const fetchDebugLogFromAPI = async (requestId: string): Promise<DebugLog> => {
  const response = await axios.get(`/api/debug/${requestId}`);
  return response.data;
};
