import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  XCircle,
  Clock,
  Database,
  ArrowRight,
  Bug,
} from "lucide-react";

const DebugDemo = () => {
  const scenarios = [
    {
      id: "demo-123",
      title: "Successful Product Lookup",
      description:
        "User searches for dining tables under 50k - successful response",
      status: "success",
      metrics: {
        latency: "2.1s",
        cache: "miss",
        fallback: false,
      },
    },
    {
      id: "xyz-123",
      title: "Order Status Inquiry",
      description: "User checks order status - fast cached response",
      status: "success",
      metrics: {
        latency: "0.8s",
        cache: "hit",
        fallback: false,
      },
    },
    {
      id: "error-456",
      title: "Failed Product Search",
      description:
        "User searches for non-existent product - fallback triggered",
      status: "error",
      metrics: {
        latency: "4.2s",
        cache: "miss",
        fallback: true,
      },
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "error":
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Success
          </Badge>
        );
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
              <Bug className="w-6 h-6 text-teal-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                AI Agent Debug Panel Demo
              </h1>
              <p className="text-gray-600 mt-1">
                Explore different debug scenarios to see how the AI agent
                performs
              </p>
            </div>
          </div>

          <div className="bg-teal-50 border border-teal-200 rounded-lg p-4">
            <p className="text-sm text-teal-800">
              <strong>Demo Instructions:</strong> Click on any scenario below to
              open the debug panel and see real-time execution details including
              intent parsing, tool invocation, and performance metrics.
            </p>
          </div>
        </div>
      </div>

      {/* Scenarios Grid */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {scenarios.map((scenario) => (
            <Card
              key={scenario.id}
              className="hover:shadow-lg transition-shadow duration-200 flex flex-col h-full"
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(scenario.status)}
                    <CardTitle className="text-lg">{scenario.title}</CardTitle>
                  </div>
                  {getStatusBadge(scenario.status)}
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {scenario.description}
                </p>
              </CardHeader>

              <CardContent className="flex flex-col justify-between flex-grow space-y-4">
                {/* Metrics */}
                <div className="grid grid-cols-3 gap-3 text-xs">
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <Clock className="w-4 h-4 mx-auto mb-1 text-gray-500" />
                    <div className="font-medium">
                      {scenario.metrics.latency}
                    </div>
                    <div className="text-gray-500">Latency</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <Database className="w-4 h-4 mx-auto mb-1 text-gray-500" />
                    <div className="font-medium capitalize">
                      {scenario.metrics.cache}
                    </div>
                    <div className="text-gray-500">Cache</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <div
                      className={`w-4 h-4 mx-auto mb-1 rounded-full ${
                        scenario.metrics.fallback
                          ? "bg-yellow-500"
                          : "bg-green-500"
                      }`}
                    />
                    <div className="font-medium">
                      {scenario.metrics.fallback ? "Yes" : "No"}
                    </div>
                    <div className="text-gray-500">Fallback</div>
                  </div>
                </div>

                {/* Action Button */}
                <Button
                  className="w-full flex justify-center items-center bg-teal-600 hover:bg-teal-700 text-white"
                  onClick={() =>
                    window.open(`/agent-debug/${scenario.id}`, "_blank")
                  }
                >
                  Open Debug Panel
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DebugDemo;
