import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import ChatMockWindow from '@/components/ChatMockWindow';
import AgentDebugPanel from '@/components/AgentDebugPanel';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const AgentDebug = () => {
  const { requestId } = useParams<{ requestId: string }>();
  const [isPanelCollapsed, setIsPanelCollapsed] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">AI Agent Debug Panel</h1>
            <p className="text-sm text-gray-600 mt-1">
              Request ID: <span className="font-mono text-teal-600">{requestId}</span>
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPanelCollapsed(!isPanelCollapsed)}
            className="flex items-center gap-2"
          >
            {isPanelCollapsed ? (
              <>
                <ChevronLeft className="w-4 h-4" />
                Show Debug Panel
              </>
            ) : (
              <>
                <ChevronRight className="w-4 h-4" />
                Hide Debug Panel
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Chat Window - Left Side */}
        <div className={`transition-all duration-300 ${
          isPanelCollapsed ? 'w-full' : 'w-[70%]'
        }`}>
          <ChatMockWindow requestId={requestId || ''} />
        </div>

        {/* Debug Panel - Right Side */}
        {!isPanelCollapsed && (
          <div className="w-[30%] border-l border-gray-200 bg-white">
            <AgentDebugPanel requestId={requestId || ''} />
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentDebug;
