import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, Bot, User } from "lucide-react";

interface Message {
  id: string;
  type: "user" | "agent" | "system";
  content: string;
  timestamp: Date;
}

interface ChatMockWindowProps {
  requestId: string;
}

const ChatMockWindow: React.FC<ChatMockWindowProps> = ({ requestId }) => {
  const getInitialMessages = (requestId: string): Message[] => {
    const baseMessages = [
      {
        id: "1",
        type: "system" as const,
        content: "Welcome to Briskk AI Agent! How can I help you today?",
        timestamp: new Date(Date.now() - 300000),
      },
    ];

    switch (requestId) {
      case "xyz-123":
        return [
          ...baseMessages,
          {
            id: "2",
            type: "user" as const,
            content: "What's my order status for #ORD12345?",
            timestamp: new Date(Date.now() - 120000),
          },
          {
            id: "3",
            type: "agent" as const,
            content:
              "I found your order #ORD12345! Here are the details:\n\n📦 **Order Status**: Out for Delivery\n📅 **Expected Delivery**: Today by 6 PM\n🚚 **Tracking ID**: TRK789456123\n\n**Items Ordered:**\n• Premium Sofa Set (3+2) - ₹45,000\n• Coffee Table - ₹8,500\n\nYour order is currently with our delivery partner and should reach you within the next few hours. You'll receive an SMS with the delivery executive's contact details shortly.",
            timestamp: new Date(Date.now() - 60000),
          },
        ];

      case "error-456":
        return [
          ...baseMessages,
          {
            id: "2",
            type: "user" as const,
            content: "Show me purple unicorns",
            timestamp: new Date(Date.now() - 120000),
          },
          {
            id: "3",
            type: "agent" as const,
            content:
              "I'm sorry, but I couldn't find any products matching \"purple unicorns\" in our furniture catalog. 🦄\n\nHowever, I can help you find:\n• **Purple furniture** (chairs, sofas, cushions)\n• **Decorative items** in purple\n• **Kids furniture** with fun designs\n\nWould you like me to show you any of these alternatives?",
            timestamp: new Date(Date.now() - 60000),
          },
        ];

      default: // demo-123
        return [
          ...baseMessages,
          {
            id: "2",
            type: "user" as const,
            content: "Show me dining tables under 50k",
            timestamp: new Date(Date.now() - 120000),
          },
          {
            id: "3",
            type: "agent" as const,
            content:
              "I found 4 dining tables under ₹50,000 for you! Here are some great options:\n\n1. **Wooden 6-Seater Dining Table** - ₹35,999\n   • Solid wood construction\n   • Seats 6 people comfortably\n\n2. **Modern Glass Top Table** - ₹42,500\n   • Tempered glass top\n   • Steel frame\n\n3. **Extendable Oak Table** - ₹48,000\n   • Extends from 4 to 8 seats\n   • Premium oak finish\n\n4. **Compact 4-Seater** - ₹28,999\n   • Perfect for small spaces\n   • Includes 4 matching chairs\n\nWould you like more details about any of these options?",
            timestamp: new Date(Date.now() - 60000),
          },
        ];
    }
  };

  const [messages, setMessages] = useState<Message[]>(
    getInitialMessages(requestId)
  );

  const [newMessage, setNewMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    setMessages(getInitialMessages(requestId));
  }, [requestId]);

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const userMessage: Message = {
        id: Date.now().toString(),
        type: "user",
        content: newMessage,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, userMessage]);
      setNewMessage("");

      // Simulate agent response after a delay
      setTimeout(() => {
        const agentMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: "agent",
          content: "Thank you for your message! I'm processing your request...",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, agentMessage]);
      }, 1000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  };

  return (
    <div className="flex flex-col h-full bg-gray-100">
      {/* Chat Header */}
      <div className="bg-teal-600 text-white p-4 flex items-center gap-3">
        <div className="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center">
          <Bot className="w-6 h-6" />
        </div>
        <div>
          <h3 className="font-semibold">Briskk AI Agent</h3>
          <p className="text-sm text-teal-100">
            Online • Typically replies instantly
          </p>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${
              message.type === "user" ? "justify-end" : "justify-start"
            }`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.type === "user"
                  ? "bg-teal-600 text-white"
                  : message.type === "system"
                  ? "bg-gray-200 text-gray-700 text-center text-sm"
                  : "bg-white text-gray-900 shadow-sm border"
              }`}
            >
              {message.type !== "system" && (
                <div className="flex items-center gap-2 mb-1">
                  {message.type === "agent" ? (
                    <Bot className="w-4 h-4 text-teal-600" />
                  ) : (
                    <User className="w-4 h-4" />
                  )}
                  <span className="text-xs font-medium">
                    {message.type === "agent" ? "Briskk AI" : "You"}
                  </span>
                </div>
              )}
              <div className="whitespace-pre-wrap">{message.content}</div>
              <div
                className={`text-xs mt-1 ${
                  message.type === "user" ? "text-teal-100" : "text-gray-500"
                }`}
              >
                {formatTime(message.timestamp)}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex gap-2">
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            className="bg-teal-600 hover:bg-teal-700"
            size="icon"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatMockWindow;
