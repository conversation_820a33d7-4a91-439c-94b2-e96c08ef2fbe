import React, { useState } from 'react';
import { useDebugLogFetcher } from '@/hooks/useDebugLogFetcher';
import DebugSidebar from '@/components/DebugSidebar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Copy, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
interface AgentDebugPanelProps {
  requestId: string;
}

const AgentDebugPanel: React.FC<AgentDebugPanelProps> = ({ requestId }) => {
  const { debugLog, isLoading, error, refetch } = useDebugLogFetcher({ requestId });
  const [activeTab, setActiveTab] = useState('visual');
  const { toast } = useToast();

  const copyRequestId = () => {
    navigator.clipboard.writeText(requestId);
    toast({
      title: "Copied!",
      description: "Request ID copied to clipboard",
      duration: 2000,
    });
  };

  const copyRawJSON = () => {
    if (debugLog) {
      navigator.clipboard.writeText(JSON.stringify(debugLog, null, 2));
      toast({
        title: "Copied!",
        description: "Raw JSON copied to clipboard",
        duration: 2000,
      });
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Panel Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Debug Panel</h2>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refetch}
              disabled={isLoading}
              className="flex items-center gap-1"
            >
              <RefreshCw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={copyRequestId}
              className="flex items-center gap-1"
            >
              <Copy className="w-3 h-3" />
              Copy ID
            </Button>
          </div>
        </div>
        
        {/* Status Indicator */}
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${
            isLoading ? 'bg-yellow-500 animate-pulse' : 
            error ? 'bg-red-500' : 'bg-green-500'
          }`} />
          <span className="text-sm text-gray-600">
            {isLoading ? 'Fetching...' : error ? 'Error' : 'Live'}
          </span>
          {debugLog?.timestamp && (
            <span className="text-xs text-gray-500 ml-auto">
              Last updated: {new Date(debugLog.timestamp).toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-2 mx-4 mt-4">
            <TabsTrigger value="visual">Visual View</TabsTrigger>
            <TabsTrigger value="raw">Raw JSON</TabsTrigger>
          </TabsList>
          
          <TabsContent value="visual" className="flex-1 overflow-y-auto p-4 space-y-4">
            {error ? (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <p className="text-red-700 text-sm">{error}</p>
                </CardContent>
              </Card>
            ) : (
              <DebugSidebar debugLog={debugLog} isLoading={isLoading} />
            )}
          </TabsContent>
          
          <TabsContent value="raw" className="flex-1 overflow-y-auto p-4">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">Raw Debug Data</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyRawJSON}
                    disabled={!debugLog}
                    className="flex items-center gap-1"
                  >
                    <Copy className="w-3 h-3" />
                    Copy JSON
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-gray-100 p-3 rounded-md overflow-x-auto whitespace-pre-wrap">
                  {debugLog ? JSON.stringify(debugLog, null, 2) : 'No data available'}
                </pre>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AgentDebugPanel;
